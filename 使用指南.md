# 数据可视化项目使用指南

## 快速开始

### 1. 环境要求
确保您的Python环境中安装了以下库：
```bash
pip install pandas matplotlib seaborn numpy openpyxl
```

### 2. 文件结构
```
项目目录/
├── 数据可视化数据集-A/
│   ├── 二手房数据.xlsx
│   ├── 某餐厅顾客消费记录.xlsx
│   └── 营销和产品销售表.xlsx
├── visualization_analysis.py
├── 数据可视化分析报告.md
├── 代码实现详解.md
└── 使用指南.md
```

### 3. 运行代码
```bash
python visualization_analysis.py
```

## 功能模块说明

### 主要函数

#### 1. `load_datasets()`
**功能**：加载所有数据集
**返回**：包含三个数据集的字典
```python
datasets = load_datasets()
# datasets['house'] - 二手房数据
# datasets['restaurant'] - 餐厅数据  
# datasets['marketing'] - 营销数据
```

#### 2. `create_house_visualizations(df)`
**功能**：生成二手房数据的3张图表
**输入**：二手房数据DataFrame
**输出**：3张PNG图片
- `house_price_boxplot.png` - 箱形图
- `house_age_price_scatter.png` - 散点图
- `house_area_price_ranking.png` - 条形图

#### 3. `create_restaurant_visualizations(df)`
**功能**：生成餐厅数据的3张图表
**输入**：餐厅数据DataFrame
**输出**：3张PNG图片
- `restaurant_customer_violin.png` - 小提琴图
- `restaurant_satisfaction_heatmap.png` - 热力图
- `restaurant_customer_pie.png` - 饼图

#### 4. `create_marketing_visualizations(df)`
**功能**：生成营销数据的3张图表
**输入**：营销数据DataFrame
**输出**：3张PNG图片
- `marketing_trend_line.png` - 折线图
- `marketing_bubble_chart.png` - 气泡图
- `marketing_area_chart.png` - 面积图

## 自定义使用

### 1. 单独生成某类图表
```python
from visualization_analysis import *

# 只生成二手房图表
datasets = load_datasets()
create_house_visualizations(datasets['house'])
```

### 2. 修改配色方案
```python
# 自定义颜色
COLORS['primary'] = '#YOUR_COLOR'
COLORS['secondary'] = '#YOUR_COLOR'
```

### 3. 调整图表尺寸
```python
# 在函数中修改figsize参数
plt.figure(figsize=(16, 10))  # 更大的图表
```

### 4. 更改输出格式
```python
# 保存为不同格式
plt.savefig('filename.pdf', dpi=300, bbox_inches='tight')  # PDF格式
plt.savefig('filename.svg', dpi=300, bbox_inches='tight')  # SVG格式
```

## 数据要求

### 二手房数据格式
必需列：
- `所在区` (字符串) - 房产所在区域
- `户型（室）` (整数) - 房间数量
- `面积（平方米）` (浮点数) - 房产面积
- `房龄（年）` (整数) - 房产年龄
- `单价（元/平方米）` (整数) - 每平米价格
- `总价（万元）` (浮点数) - 总价格

### 餐厅数据格式
必需列：
- `分店` (字符串) - 分店名称
- `顾客类型` (字符串) - 会员/普通顾客
- `性别` (字符串) - 顾客性别
- `消费金额（元）` (浮点数) - 消费金额
- `顾客满意度` (整数) - 满意度评分

### 营销数据格式
必需列：
- `日期` (日期时间) - 数据日期
- `营销费用（元）` (浮点数) - 营销投入
- `展现量` (整数) - 广告展现次数
- `点击量` (整数) - 广告点击次数
- `订单金额（元）` (浮点数) - 订单金额
- `加购数` (整数) - 加购商品数量
- `进店数` (整数) - 进店人数
- `商品关注数` (整数) - 商品关注数

## 常见问题解决

### 1. 中文显示问题
如果中文显示为方块，请检查字体设置：
```python
import matplotlib.pyplot as plt
print(plt.rcParams['font.sans-serif'])  # 查看当前字体设置

# 手动设置字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
```

### 2. 内存不足问题
对于大数据集，建议：
```python
# 分批处理数据
chunk_size = 1000
for chunk in pd.read_excel('large_file.xlsx', chunksize=chunk_size):
    # 处理每个数据块
    pass
```

### 3. 图表显示不完整
```python
# 调整布局
plt.tight_layout()
# 或者手动调整边距
plt.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)
```

### 4. 保存图片质量问题
```python
# 提高DPI设置
plt.savefig('filename.png', dpi=600, bbox_inches='tight')
# 使用矢量格式
plt.savefig('filename.pdf', bbox_inches='tight')
```

## 性能优化建议

### 1. 数据预处理
```python
# 在绘图前完成数据清洗
df = df.dropna()  # 删除缺失值
df = df[df['价格'] > 0]  # 删除异常值
```

### 2. 内存管理
```python
# 及时关闭图形
plt.close('all')
# 或者使用上下文管理器
with plt.style.context('seaborn'):
    # 绘图代码
    pass
```

### 3. 批量处理
```python
# 批量生成多个图表
for dataset_name, df in datasets.items():
    create_visualizations(df, dataset_name)
```

## 扩展功能

### 1. 交互式图表
```python
import plotly.express as px

# 创建交互式散点图
fig = px.scatter(df, x='房龄', y='单价', size='面积', color='总价')
fig.show()
```

### 2. 动画图表
```python
import matplotlib.animation as animation

# 创建动画
def animate(frame):
    # 动画逻辑
    pass

ani = animation.FuncAnimation(fig, animate, frames=100)
```

### 3. 3D图表
```python
from mpl_toolkits.mplot3d import Axes3D

fig = plt.figure()
ax = fig.add_subplot(111, projection='3d')
ax.scatter(x, y, z)
```

## 最佳实践

### 1. 代码组织
- 将配置参数放在文件顶部
- 使用函数封装重复代码
- 添加适当的注释和文档字符串

### 2. 图表设计
- 保持配色一致性
- 使用清晰的标题和标签
- 适当使用网格线和图例
- 确保文字大小适中

### 3. 数据处理
- 始终检查数据质量
- 处理缺失值和异常值
- 进行适当的数据变换

### 4. 输出管理
- 使用有意义的文件名
- 保存高质量图片
- 备份重要结果

## 技术支持

如果遇到问题，请检查：
1. Python版本兼容性（推荐3.7+）
2. 库版本兼容性
3. 数据格式是否正确
4. 文件路径是否正确

---

**更新时间**：2024年12月  
**适用版本**：Python 3.7+  
**依赖库**：pandas, matplotlib, seaborn, numpy  
**支持平台**：Windows, macOS, Linux
