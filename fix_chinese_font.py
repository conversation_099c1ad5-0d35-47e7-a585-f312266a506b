import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import pandas as pd
import seaborn as sns
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def find_and_set_chinese_font():
    """查找并设置最佳中文字体"""
    print("正在检查系统中的中文字体...")
    
    # 获取所有字体
    font_list = fm.fontManager.ttflist
    chinese_fonts = []
    
    # 查找中文字体
    chinese_keywords = ['SimHei', 'SimSun', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'DengXian', '微软雅黑', '黑体', '宋体']
    
    for font in font_list:
        font_name = font.name
        if any(keyword in font_name for keyword in chinese_keywords):
            chinese_fonts.append(font_name)
    
    chinese_fonts = list(set(chinese_fonts))
    print(f"找到的中文字体: {chinese_fonts}")
    
    # 设置字体优先级
    preferred_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
    selected_font = None
    
    for pref_font in preferred_fonts:
        if pref_font in chinese_fonts:
            selected_font = pref_font
            break
    
    if not selected_font and chinese_fonts:
        selected_font = chinese_fonts[0]
    
    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font]
        plt.rcParams['axes.unicode_minus'] = False
        print(f"✅ 已设置中文字体: {selected_font}")
        return selected_font
    else:
        print("❌ 未找到合适的中文字体")
        # 尝试使用系统默认字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        return None

def test_chinese_display():
    """测试中文显示效果"""
    print("正在测试中文显示效果...")
    
    plt.figure(figsize=(10, 6))
    
    # 测试各种中文文本
    test_texts = [
        "数据可视化分析报告",
        "北京各区域二手房单价分布对比", 
        "营销费用与订单金额时间趋势",
        "顾客类型分布",
        "消费金额（元）",
        "单价（元/平方米）"
    ]
    
    for i, text in enumerate(test_texts):
        plt.text(0.1, 0.9 - i*0.12, text, fontsize=14, transform=plt.gca().transAxes)
    
    plt.title('中文字体显示测试', fontsize=16, fontweight='bold')
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig('chinese_font_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 中文字体测试图片已保存: chinese_font_test.png")

def create_sample_chart():
    """创建一个包含中文的示例图表"""
    print("正在创建包含中文的示例图表...")
    
    # 创建示例数据
    data = {
        '区域': ['朝阳区', '海淀区', '西城区', '东城区', '丰台区'],
        '平均房价': [65000, 72000, 95000, 88000, 45000],
        '样本数量': [450, 380, 220, 180, 320]
    }
    df = pd.DataFrame(data)
    
    plt.figure(figsize=(12, 8))
    
    # 创建条形图
    bars = plt.bar(df['区域'], df['平均房价'], 
                   color=['#2E86AB', '#A23B72', '#F18F01', '#A8DADC', '#1D3557'],
                   alpha=0.8)
    
    # 添加数值标签
    for i, (area, price, count) in enumerate(zip(df['区域'], df['平均房价'], df['样本数量'])):
        plt.text(i, price + 1000, f'{price:,}', ha='center', va='bottom', fontweight='bold')
        plt.text(i, price/2, f'样本数: {count}', ha='center', va='center', 
                color='white', fontweight='bold', fontsize=10)
    
    plt.title('北京各区域二手房平均单价对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('区域', fontsize=12, fontweight='bold')
    plt.ylabel('平均单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')
    
    # 格式化Y轴标签
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000:.0f}K'))
    
    plt.tight_layout()
    plt.savefig('sample_chinese_chart.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 示例中文图表已保存: sample_chinese_chart.png")

def regenerate_all_charts():
    """重新生成所有图表，确保中文正确显示"""
    print("正在重新生成所有可视化图表...")
    
    # 导入原始的可视化函数
    from visualization_analysis import load_datasets, COLORS
    
    # 加载数据
    datasets = load_datasets()
    
    # 重新生成二手房图表
    print("生成二手房数据图表...")
    create_house_charts_fixed(datasets['house'])
    
    # 重新生成餐厅图表
    print("生成餐厅数据图表...")
    create_restaurant_charts_fixed(datasets['restaurant'])
    
    # 重新生成营销图表
    print("生成营销数据图表...")
    create_marketing_charts_fixed(datasets['marketing'])
    
    print("✅ 所有图表重新生成完成！")

def create_house_charts_fixed(df):
    """重新生成二手房图表（修复中文显示）"""
    
    # 1. 箱形图
    plt.figure(figsize=(14, 8))
    area_counts = df['所在区'].value_counts()
    top_areas = area_counts.head(8).index.tolist()
    df_filtered = df[df['所在区'].isin(top_areas)]
    
    box_plot = plt.boxplot([df_filtered[df_filtered['所在区'] == area]['单价（元/平方米）'].values 
                           for area in top_areas], 
                          labels=top_areas, patch_artist=True)
    
    colors = plt.cm.Blues(np.linspace(0.4, 0.8, len(top_areas)))
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    plt.title('北京各区域二手房单价分布对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('区域', fontsize=12, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('house_price_boxplot_fixed.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 散点图
    plt.figure(figsize=(12, 8))
    scatter = plt.scatter(df['房龄（年）'], df['单价（元/平方米）'], 
                         s=df['面积（平方米）']*2, 
                         c=df['总价（万元）'], 
                         cmap='viridis', alpha=0.6, edgecolors='white', linewidth=0.5)
    
    plt.colorbar(scatter, label='总价（万元）')
    plt.title('二手房房龄与单价关系分析\n（气泡大小表示面积，颜色表示总价）', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('房龄（年）', fontsize=12, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(df['房龄（年）'], df['单价（元/平方米）'], 1)
    p = np.poly1d(z)
    plt.plot(df['房龄（年）'], p(df['房龄（年）']), "r--", alpha=0.8, linewidth=2)
    
    correlation = df['房龄（年）'].corr(df['单价（元/平方米）'])
    plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
             transform=plt.gca().transAxes, fontsize=12, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('house_age_price_scatter_fixed.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_restaurant_charts_fixed(df):
    """重新生成餐厅图表（修复中文显示）"""
    
    # 饼图
    plt.figure(figsize=(10, 8))
    customer_counts = df['顾客类型'].value_counts()
    
    colors = ['#2E86AB', '#A23B72']
    wedges, texts, autotexts = plt.pie(customer_counts.values, 
                                      labels=customer_counts.index,
                                      autopct='%1.1f%%',
                                      colors=colors,
                                      explode=(0.05, 0.05),
                                      shadow=True,
                                      startangle=90)
    
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)
    
    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')
    
    plt.title('餐厅顾客类型分布', fontsize=16, fontweight='bold', pad=20)
    
    plt.legend(wedges, [f'{label}: {count}人' for label, count in customer_counts.items()],
              title="顾客类型统计",
              loc="center left",
              bbox_to_anchor=(1, 0, 0.5, 1))
    
    plt.tight_layout()
    plt.savefig('restaurant_customer_pie_fixed.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_marketing_charts_fixed(df):
    """重新生成营销图表（修复中文显示）"""
    
    # 折线图
    fig, ax1 = plt.subplots(figsize=(14, 8))
    
    color1 = '#2E86AB'
    ax1.set_xlabel('日期', fontsize=12, fontweight='bold')
    ax1.set_ylabel('营销费用（元）', color=color1, fontsize=12, fontweight='bold')
    line1 = ax1.plot(df['日期'], df['营销费用（元）'], color=color1, linewidth=3,
                     marker='o', markersize=6, label='营销费用')
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.grid(True, alpha=0.3)
    
    ax2 = ax1.twinx()
    color2 = '#A23B72'
    ax2.set_ylabel('订单金额（元）', color=color2, fontsize=12, fontweight='bold')
    line2 = ax2.plot(df['日期'], df['订单金额（元）'], color=color2, linewidth=3,
                     marker='s', markersize=6, label='订单金额')
    ax2.tick_params(axis='y', labelcolor=color2)
    
    ax1.tick_params(axis='x', rotation=45)
    
    plt.title('营销费用与订单金额时间趋势对比', fontsize=16, fontweight='bold', pad=20)
    
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')
    
    plt.tight_layout()
    plt.savefig('marketing_trend_line_fixed.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    print("=" * 60)
    print("中文字体修复工具")
    print("=" * 60)
    
    # 1. 查找并设置中文字体
    selected_font = find_and_set_chinese_font()
    
    # 2. 测试中文显示
    test_chinese_display()
    
    # 3. 创建示例图表
    create_sample_chart()
    
    # 4. 重新生成部分关键图表
    try:
        regenerate_all_charts()
    except Exception as e:
        print(f"重新生成图表时出错: {e}")
        print("请手动运行修复后的可视化代码")
    
    print("=" * 60)
    print("中文字体修复完成！")
    print("请检查生成的测试图片确认中文显示是否正常")
    print("=" * 60)
