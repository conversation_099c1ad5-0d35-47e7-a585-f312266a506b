# 数据可视化代码实现详解

## 项目架构

本项目采用模块化设计，主要包含以下几个核心函数：

1. `load_datasets()` - 数据加载函数
2. `create_house_visualizations()` - 二手房数据可视化
3. `create_restaurant_visualizations()` - 餐厅数据可视化  
4. `create_marketing_visualizations()` - 营销数据可视化

## 核心配置

### 1. 环境配置
```python
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-darkgrid')
```

### 2. 冷色调配色方案
```python
COLORS = {
    'primary': '#2E86AB',      # 深蓝色
    'secondary': '#A23B72',    # 深紫色  
    'accent': '#F18F01',       # 橙色
    'light_blue': '#A8DADC',   # 浅蓝色
    'dark_blue': '#1D3557',    # 深蓝色
    'teal': '#457B9D',         # 青蓝色
    'palette': ['#2E86AB', '#A23B72', '#F18F01', '#A8DADC', '#1D3557', '#457B9D', '#E63946', '#F77F00']
}
```

## 详细实现解析

### 图表1: 箱形图 - 房价分布对比

**技术要点：**
- 使用`plt.boxplot()`创建箱形图
- 通过`patch_artist=True`启用颜色填充
- 使用`plt.cm.Blues()`生成渐变色彩
- 添加中位数标注

**关键代码：**
```python
def create_house_boxplot(df):
    plt.figure(figsize=(14, 8))
    
    # 筛选数据量较多的区域
    area_counts = df['所在区'].value_counts()
    top_areas = area_counts.head(8).index.tolist()
    df_filtered = df[df['所在区'].isin(top_areas)]
    
    # 创建箱形图
    box_plot = plt.boxplot([df_filtered[df_filtered['所在区'] == area]['单价（元/平方米）'].values 
                           for area in top_areas], 
                          labels=top_areas, patch_artist=True)
    
    # 设置渐变色彩
    colors = plt.cm.Blues(np.linspace(0.4, 0.8, len(top_areas)))
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
```

### 图表2: 散点图 - 房龄与价格关系

**技术要点：**
- 多维数据展示：X轴(房龄)、Y轴(单价)、点大小(面积)、颜色(总价)
- 添加趋势线分析
- 计算并显示相关系数

**关键代码：**
```python
def create_age_price_scatter(df):
    plt.figure(figsize=(12, 8))
    
    # 创建多维散点图
    scatter = plt.scatter(df['房龄（年）'], df['单价（元/平方米）'], 
                         s=df['面积（平方米）']*2,  # 点大小
                         c=df['总价（万元）'],      # 颜色
                         cmap='viridis', alpha=0.6, 
                         edgecolors='white', linewidth=0.5)
    
    # 添加趋势线
    z = np.polyfit(df['房龄（年）'], df['单价（元/平方米）'], 1)
    p = np.poly1d(z)
    plt.plot(df['房龄（年）'], p(df['房龄（年）']), "r--", alpha=0.8, linewidth=2)
    
    # 计算相关系数
    correlation = df['房龄（年）'].corr(df['单价（元/平方米）'])
    plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
             transform=plt.gca().transAxes, fontsize=12)
```

### 图表3: 条形图 - 区域房价排名

**技术要点：**
- 数据排序和筛选
- 渐变色条形图
- 双重标注（价格+样本数）

**关键代码：**
```python
def create_area_ranking(df):
    # 计算平均房价并排序
    area_avg_price = df.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=False)
    area_counts = df['所在区'].value_counts()
    
    # 筛选有效区域
    valid_areas = area_counts[area_counts >= 50].index
    area_avg_price = area_avg_price[area_avg_price.index.isin(valid_areas)]
    
    # 创建渐变色条形图
    bars = plt.bar(range(len(area_avg_price)), area_avg_price.values, 
                   color=plt.cm.Blues(np.linspace(0.4, 0.9, len(area_avg_price))))
```

### 图表4: 小提琴图 - 消费分布对比

**技术要点：**
- 使用`plt.violinplot()`展示分布形状
- 显示均值和中位数
- 自定义颜色和透明度

**关键代码：**
```python
def create_customer_violin(df):
    # 创建小提琴图
    violin_parts = plt.violinplot([df[df['顾客类型'] == customer_type]['消费金额（元）'].values 
                                  for customer_type in df['顾客类型'].unique()], 
                                 positions=range(len(df['顾客类型'].unique())), 
                                 showmeans=True, showmedians=True)
    
    # 设置颜色
    colors = [COLORS['primary'], COLORS['secondary']]
    for i, pc in enumerate(violin_parts['bodies']):
        pc.set_facecolor(colors[i % len(colors)])
        pc.set_alpha(0.7)
```

### 图表5: 热力图 - 满意度分析

**技术要点：**
- 使用`pivot_table`重构数据
- `seaborn.heatmap()`创建热力图
- 自定义色彩映射和标注

**关键代码：**
```python
def create_satisfaction_heatmap(df):
    # 创建透视表
    pivot_table = df.pivot_table(values='顾客满意度', 
                                index='分店', 
                                columns='顾客类型', 
                                aggfunc='mean')
    
    # 创建热力图
    sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                center=pivot_table.values.mean(), 
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
```

### 图表6: 饼图 - 顾客类型分布

**技术要点：**
- 3D效果和阴影
- 自定义标签和百分比显示
- 图例位置优化

**关键代码：**
```python
def create_customer_pie(df):
    customer_counts = df['顾客类型'].value_counts()
    
    # 创建饼图
    colors = [COLORS['primary'], COLORS['secondary']]
    wedges, texts, autotexts = plt.pie(customer_counts.values, 
                                      labels=customer_counts.index,
                                      autopct='%1.1f%%',
                                      colors=colors,
                                      explode=(0.05, 0.05),
                                      shadow=True,
                                      startangle=90)
```

### 图表7: 折线图 - 营销趋势对比

**技术要点：**
- 双Y轴设计
- 不同颜色和标记样式
- 图例合并显示

**关键代码：**
```python
def create_marketing_trend(df):
    fig, ax1 = plt.subplots(figsize=(14, 8))
    
    # 左Y轴
    color1 = COLORS['primary']
    ax1.plot(df['日期'], df['营销费用（元）'], color=color1, linewidth=3,
             marker='o', markersize=6, label='营销费用')
    
    # 右Y轴
    ax2 = ax1.twinx()
    color2 = COLORS['secondary']
    ax2.plot(df['日期'], df['订单金额（元）'], color=color2, linewidth=3,
             marker='s', markersize=6, label='订单金额')
```

### 图表8: 气泡图 - 三维关系分析

**技术要点：**
- 四维数据展示
- 相关系数计算和显示
- 颜色条标注

**关键代码：**
```python
def create_bubble_chart(df):
    # 创建气泡图
    scatter = plt.scatter(df['展现量'], df['点击量'], 
                         s=df['订单金额（元）']*0.5,  # 气泡大小
                         c=df['营销费用（元）'],       # 颜色
                         cmap='viridis', alpha=0.7, 
                         edgecolors='white', linewidth=1)
    
    # 添加相关系数
    corr_show_click = df['展现量'].corr(df['点击量'])
    corr_click_order = df['点击量'].corr(df['订单金额（元）'])
```

### 图表9: 面积图 - 指标变化趋势

**技术要点：**
- 数据标准化处理
- 堆叠面积图
- 多指标同时展示

**关键代码：**
```python
def create_area_chart(df):
    metrics = ['展现量', '点击量', '加购数', '进店数', '商品关注数']
    df_normalized = df.copy()
    
    # 标准化处理
    for metric in metrics:
        df_normalized[f'{metric}_norm'] = (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min())
    
    # 创建面积图
    plt.stackplot(df['日期'], 
                  df_normalized['展现量_norm'],
                  df_normalized['点击量_norm'], 
                  df_normalized['加购数_norm'],
                  df_normalized['进店数_norm'],
                  df_normalized['商品关注数_norm'],
                  labels=metrics,
                  colors=COLORS['palette'][:5],
                  alpha=0.8)
```

## 通用设计规范

### 1. 标题和标签设置
```python
plt.title('图表标题', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('X轴标签', fontsize=12, fontweight='bold')
plt.ylabel('Y轴标签', fontsize=12, fontweight='bold')
```

### 2. 网格和布局
```python
plt.grid(True, alpha=0.3)
plt.tight_layout()
```

### 3. 高质量输出
```python
plt.savefig('filename.png', dpi=300, bbox_inches='tight')
plt.show()
```

## 数据处理技巧

### 1. 数据筛选
```python
# 按数据量筛选
area_counts = df['所在区'].value_counts()
valid_areas = area_counts[area_counts >= 50].index
```

### 2. 数据标准化
```python
# 0-1标准化
normalized = (data - data.min()) / (data.max() - data.min())
```

### 3. 透视表创建
```python
pivot_table = df.pivot_table(values='目标列', 
                            index='行索引', 
                            columns='列索引', 
                            aggfunc='mean')
```

## 性能优化

1. **数据预处理**：在绘图前完成所有数据清洗和计算
2. **内存管理**：及时关闭图形对象释放内存
3. **批量处理**：将相似的图表操作合并处理
4. **缓存机制**：对重复计算的结果进行缓存

---

**代码总行数**：约350行  
**函数数量**：4个主要函数 + 多个辅助函数  
**图表类型**：9种不同类型的专业图表  
**设计原则**：统一性、专业性、可读性、美观性
