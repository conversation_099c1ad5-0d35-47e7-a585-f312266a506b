import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体 - 简化版本
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False

print("✓ 中文字体设置完成")

# 设置冷色调配色方案
COLORS = {
    'primary': '#2E86AB',      # 深蓝色
    'secondary': '#A23B72',    # 深紫色
    'accent': '#F18F01',       # 橙色
    'light_blue': '#A8DADC',   # 浅蓝色
    'dark_blue': '#1D3557',    # 深蓝色
    'teal': '#457B9D',         # 青蓝色
    'palette': ['#2E86AB', '#A23B72', '#F18F01', '#A8DADC', '#1D3557', '#457B9D', '#E63946', '#F77F00']
}

def load_datasets():
    """加载所有数据集"""
    datasets = {}

    try:
        # 加载二手房数据
        datasets['house'] = pd.read_excel('数据可视化数据集-A/二手房数据.xlsx')
        print("✓ 二手房数据加载成功")

        # 加载餐厅消费记录
        datasets['restaurant'] = pd.read_excel('数据可视化数据集-A/某餐厅顾客消费记录.xlsx')
        print("✓ 餐厅数据加载成功")

        # 加载营销数据
        datasets['marketing'] = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
        print("✓ 营销数据加载成功")

    except Exception as e:
        print(f"数据加载错误: {e}")

    return datasets

def create_house_visualizations(df):
    """创建二手房数据的可视化图表"""

    print("\n开始生成二手房数据可视化图表...")

    # 1. 箱形图：各区域房价分布对比
    plt.figure(figsize=(14, 8))

    area_counts = df['所在区'].value_counts()
    top_areas = area_counts.head(8).index.tolist()
    df_filtered = df[df['所在区'].isin(top_areas)]

    box_data = [df_filtered[df_filtered['所在区'] == area]['单价（元/平方米）'].values
                for area in top_areas]

    box_plot = plt.boxplot(box_data, labels=top_areas, patch_artist=True)

    colors = plt.cm.Blues(np.linspace(0.4, 0.8, len(top_areas)))
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.title('北京各区域二手房单价分布对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('区域', fontsize=12, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    for i, area in enumerate(top_areas):
        area_data = df_filtered[df_filtered['所在区'] == area]['单价（元/平方米）']
        median_price = area_data.median()
        plt.text(i+1, median_price, f'{median_price:.0f}',
                ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.tight_layout()
    plt.savefig('house_price_boxplot_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 箱形图生成完成")

    # 2. 散点图：房龄与单价关系分析
    plt.figure(figsize=(12, 8))

    scatter = plt.scatter(df['房龄（年）'], df['单价（元/平方米）'],
                         s=df['面积（平方米）']*2,
                         c=df['总价（万元）'],
                         cmap='viridis', alpha=0.6, edgecolors='white', linewidth=0.5)

    plt.colorbar(scatter, label='总价（万元）')
    plt.title('二手房房龄与单价关系分析\n（气泡大小表示面积，颜色表示总价）',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('房龄（年）', fontsize=12, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.grid(True, alpha=0.3)

    z = np.polyfit(df['房龄（年）'], df['单价（元/平方米）'], 1)
    p = np.poly1d(z)
    plt.plot(df['房龄（年）'], p(df['房龄（年）']), "r--", alpha=0.8, linewidth=2)

    correlation = df['房龄（年）'].corr(df['单价（元/平方米）'])
    plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}',
             transform=plt.gca().transAxes, fontsize=12,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    plt.tight_layout()
    plt.savefig('house_age_price_scatter_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 散点图生成完成")

    # 3. 条形图：各区域平均房价排名
    plt.figure(figsize=(14, 8))

    area_avg_price = df.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=False)
    area_counts = df['所在区'].value_counts()

    valid_areas = area_counts[area_counts >= 50].index
    area_avg_price = area_avg_price[area_avg_price.index.isin(valid_areas)]

    plt.bar(range(len(area_avg_price)), area_avg_price.values,
            color=plt.cm.Blues(np.linspace(0.4, 0.9, len(area_avg_price))))

    plt.title('北京各区域二手房平均单价排名', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('区域', fontsize=12, fontweight='bold')
    plt.ylabel('平均单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.xticks(range(len(area_avg_price)), area_avg_price.index, rotation=45)
    plt.grid(True, alpha=0.3, axis='y')

    for i, (area, price) in enumerate(area_avg_price.items()):
        plt.text(i, price + 1000, f'{price:.0f}',
                ha='center', va='bottom', fontweight='bold', fontsize=10)
        count = area_counts[area]
        plt.text(i, price/2, f'n={count}',
                ha='center', va='center', fontsize=8, color='white', fontweight='bold')

    plt.tight_layout()
    plt.savefig('house_area_price_ranking_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 条形图生成完成")

def create_restaurant_visualizations(df):
    """创建餐厅消费数据的可视化图表"""

    print("\n开始生成餐厅数据可视化图表...")

    # 4. 小提琴图：不同顾客类型的消费金额分布
    plt.figure(figsize=(12, 8))

    customer_types = df['顾客类型'].unique()
    violin_data = [df[df['顾客类型'] == customer_type]['消费金额（元）'].values
                   for customer_type in customer_types]

    violin_parts = plt.violinplot(violin_data, positions=range(len(customer_types)),
                                 showmeans=True, showmedians=True)

    colors = [COLORS['primary'], COLORS['secondary']]
    for i, pc in enumerate(violin_parts['bodies']):
        pc.set_facecolor(colors[i % len(colors)])
        pc.set_alpha(0.7)

    plt.title('不同顾客类型消费金额分布对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('顾客类型', fontsize=12, fontweight='bold')
    plt.ylabel('消费金额（元）', fontsize=12, fontweight='bold')
    plt.xticks(range(len(customer_types)), customer_types)
    plt.grid(True, alpha=0.3)

    for i, customer_type in enumerate(customer_types):
        data = df[df['顾客类型'] == customer_type]['消费金额（元）']
        mean_val = data.mean()
        median_val = data.median()
        plt.text(i, mean_val, f'均值: {mean_val:.1f}',
                ha='center', va='bottom', fontsize=9, fontweight='bold')
        plt.text(i, median_val, f'中位数: {median_val:.1f}',
                ha='center', va='top', fontsize=9, fontweight='bold')

    plt.tight_layout()
    plt.savefig('restaurant_customer_violin_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 小提琴图生成完成")

    # 5. 热力图：分店与顾客满意度关系
    plt.figure(figsize=(12, 8))

    pivot_table = df.pivot_table(values='顾客满意度',
                                index='分店',
                                columns='顾客类型',
                                aggfunc='mean')

    sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlBu_r',
                center=pivot_table.values.mean(),
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})

    plt.title('各分店不同顾客类型满意度热力图', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('顾客类型', fontsize=12, fontweight='bold')
    plt.ylabel('分店', fontsize=12, fontweight='bold')
    plt.xticks(rotation=0)
    plt.yticks(rotation=0)

    plt.tight_layout()
    plt.savefig('restaurant_satisfaction_heatmap_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 热力图生成完成")

    # 6. 饼图：顾客类型占比分析
    plt.figure(figsize=(10, 8))

    customer_counts = df['顾客类型'].value_counts()

    colors = [COLORS['primary'], COLORS['secondary']]
    wedges, texts, autotexts = plt.pie(customer_counts.values,
                                      labels=customer_counts.index,
                                      autopct='%1.1f%%',
                                      colors=colors,
                                      explode=(0.05, 0.05),
                                      shadow=True,
                                      startangle=90)

    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')

    plt.title('餐厅顾客类型分布', fontsize=16, fontweight='bold', pad=20)

    plt.legend(wedges, [f'{label}: {count}人' for label, count in customer_counts.items()],
              title="顾客类型统计",
              loc="center left",
              bbox_to_anchor=(1, 0, 0.5, 1))

    plt.tight_layout()
    plt.savefig('restaurant_customer_pie_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 饼图生成完成")

def create_marketing_visualizations(df):
    """创建营销数据的可视化图表"""

    print("\n开始生成营销数据可视化图表...")

    # 7. 折线图：营销费用与订单金额时间趋势
    fig, ax1 = plt.subplots(figsize=(14, 8))

    color1 = COLORS['primary']
    ax1.set_xlabel('日期', fontsize=12, fontweight='bold')
    ax1.set_ylabel('营销费用（元）', color=color1, fontsize=12, fontweight='bold')
    line1 = ax1.plot(df['日期'], df['营销费用（元）'], color=color1, linewidth=3,
                     marker='o', markersize=6, label='营销费用')
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.grid(True, alpha=0.3)

    ax2 = ax1.twinx()
    color2 = COLORS['secondary']
    ax2.set_ylabel('订单金额（元）', color=color2, fontsize=12, fontweight='bold')
    line2 = ax2.plot(df['日期'], df['订单金额（元）'], color=color2, linewidth=3,
                     marker='s', markersize=6, label='订单金额')
    ax2.tick_params(axis='y', labelcolor=color2)

    ax1.tick_params(axis='x', rotation=45)

    plt.title('营销费用与订单金额时间趋势对比', fontsize=16, fontweight='bold', pad=20)

    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')

    plt.tight_layout()
    plt.savefig('marketing_trend_line_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 折线图生成完成")

    # 8. 气泡图：展现量、点击量与订单金额三维关系
    plt.figure(figsize=(12, 8))

    scatter = plt.scatter(df['展现量'], df['点击量'],
                         s=df['订单金额（元）']*0.5,
                         c=df['营销费用（元）'],
                         cmap='viridis', alpha=0.7,
                         edgecolors='white', linewidth=1)

    plt.colorbar(scatter, label='营销费用（元）')
    plt.title('营销效果三维关系分析\n（气泡大小表示订单金额，颜色表示营销费用）',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('展现量', fontsize=12, fontweight='bold')
    plt.ylabel('点击量', fontsize=12, fontweight='bold')
    plt.grid(True, alpha=0.3)

    corr_show_click = df['展现量'].corr(df['点击量'])
    corr_click_order = df['点击量'].corr(df['订单金额（元）'])
    plt.text(0.05, 0.95, f'展现量-点击量相关系数: {corr_show_click:.3f}',
             transform=plt.gca().transAxes, fontsize=10,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    plt.text(0.05, 0.88, f'点击量-订单金额相关系数: {corr_click_order:.3f}',
             transform=plt.gca().transAxes, fontsize=10,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    plt.tight_layout()
    plt.savefig('marketing_bubble_chart_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 气泡图生成完成")

    # 9. 面积图：各项营销指标的时间变化趋势
    plt.figure(figsize=(14, 8))

    metrics = ['展现量', '点击量', '加购数', '进店数', '商品关注数']
    df_normalized = df.copy()

    for metric in metrics:
        df_normalized[f'{metric}_norm'] = (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min())

    plt.stackplot(df['日期'],
                  df_normalized['展现量_norm'],
                  df_normalized['点击量_norm'],
                  df_normalized['加购数_norm'],
                  df_normalized['进店数_norm'],
                  df_normalized['商品关注数_norm'],
                  labels=metrics,
                  colors=COLORS['palette'][:5],
                  alpha=0.8)

    plt.title('营销指标时间变化趋势（标准化）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('日期', fontsize=12, fontweight='bold')
    plt.ylabel('标准化数值（0-1）', fontsize=12, fontweight='bold')
    plt.legend(loc='upper left', bbox_to_anchor=(1, 1))
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('marketing_area_chart_final.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 面积图生成完成")

if __name__ == "__main__":
    print("🚀 开始生成完整的可视化图表...")

    # 加载数据
    datasets = load_datasets()

    if 'house' in datasets:
        create_house_visualizations(datasets['house'])

    if 'restaurant' in datasets:
        create_restaurant_visualizations(datasets['restaurant'])

    if 'marketing' in datasets:
        create_marketing_visualizations(datasets['marketing'])

    print("\n🎉 所有9张可视化图表生成完成！")
    print("\n生成的图表文件:")
    import os
    final_images = [f for f in os.listdir('.') if f.endswith('_final.png')]
    for i, file in enumerate(sorted(final_images), 1):
        size = os.path.getsize(file) / 1024
        print(f"  {i:2d}. {file:<40} ({size:6.1f} KB)")

    print(f"\n✅ 总共生成了 {len(final_images)} 张高质量图表")
    print("✅ 所有图表都支持完美的中文显示")
    print("✅ 采用专业的冷色调配色方案")
    print("✅ 输出分辨率为300 DPI专业级质量")
