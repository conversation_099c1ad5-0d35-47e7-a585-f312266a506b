import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
import matplotlib.font_manager as fm

# 检查并设置中文字体
def setup_chinese_font():
    """设置中文字体"""
    # 获取系统中可用的中文字体
    font_list = fm.fontManager.ttflist
    chinese_fonts = []

    for font in font_list:
        font_name = font.name
        if any(keyword in font_name for keyword in ['SimHei', 'SimSun', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'DengXian']):
            chinese_fonts.append(font_name)

    chinese_fonts = list(set(chinese_fonts))

    if chinese_fonts:
        # 优先级顺序
        preferred_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
        selected_font = None

        for pref_font in preferred_fonts:
            if pref_font in chinese_fonts:
                selected_font = pref_font
                break

        if not selected_font:
            selected_font = chinese_fonts[0]

        plt.rcParams['font.sans-serif'] = [selected_font]
        print(f"已设置中文字体: {selected_font}")
    else:
        # 如果没有找到中文字体，使用默认设置
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        print("警告: 未找到中文字体，可能无法正确显示中文")

    plt.rcParams['axes.unicode_minus'] = False

# 调用字体设置函数
setup_chinese_font()
plt.style.use('seaborn-v0_8-darkgrid')

# 设置冷色调配色方案
COLORS = {
    'primary': '#2E86AB',      # 深蓝色
    'secondary': '#A23B72',    # 深紫色
    'accent': '#F18F01',       # 橙色
    'light_blue': '#A8DADC',   # 浅蓝色
    'dark_blue': '#1D3557',    # 深蓝色
    'teal': '#457B9D',         # 青蓝色
    'palette': ['#2E86AB', '#A23B72', '#F18F01', '#A8DADC', '#1D3557', '#457B9D', '#E63946', '#F77F00']
}

def load_datasets():
    """加载所有数据集"""
    datasets = {}

    # 加载二手房数据
    datasets['house'] = pd.read_excel('数据可视化数据集-A/二手房数据.xlsx')

    # 加载餐厅消费记录
    datasets['restaurant'] = pd.read_excel('数据可视化数据集-A/某餐厅顾客消费记录.xlsx')

    # 加载营销数据
    datasets['marketing'] = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')

    return datasets

def create_house_visualizations(df):
    """创建二手房数据的可视化图表"""

    # 1. 箱形图：各区域房价分布对比
    plt.figure(figsize=(14, 8))

    # 计算各区域数据量，只显示数据量较多的区域
    area_counts = df['所在区'].value_counts()
    top_areas = area_counts.head(8).index.tolist()
    df_filtered = df[df['所在区'].isin(top_areas)]

    box_plot = plt.boxplot([df_filtered[df_filtered['所在区'] == area]['单价（元/平方米）'].values
                           for area in top_areas],
                          labels=top_areas, patch_artist=True)

    # 设置箱形图颜色
    colors = plt.cm.Blues(np.linspace(0.4, 0.8, len(top_areas)))
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.title('北京各区域二手房单价分布对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('区域', fontsize=12, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    for i, area in enumerate(top_areas):
        area_data = df_filtered[df_filtered['所在区'] == area]['单价（元/平方米）']
        median_price = area_data.median()
        plt.text(i+1, median_price, f'{median_price:.0f}',
                ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.tight_layout()
    plt.savefig('house_price_boxplot.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 散点图：房龄与单价关系分析
    plt.figure(figsize=(12, 8))

    # 创建散点图，按面积大小设置点的大小
    scatter = plt.scatter(df['房龄（年）'], df['单价（元/平方米）'],
                         s=df['面积（平方米）']*2,
                         c=df['总价（万元）'],
                         cmap='viridis', alpha=0.6, edgecolors='white', linewidth=0.5)

    plt.colorbar(scatter, label='总价（万元）')
    plt.title('二手房房龄与单价关系分析\n（气泡大小表示面积，颜色表示总价）',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('房龄（年）', fontsize=12, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(df['房龄（年）'], df['单价（元/平方米）'], 1)
    p = np.poly1d(z)
    plt.plot(df['房龄（年）'], p(df['房龄（年）']), "r--", alpha=0.8, linewidth=2)

    # 添加相关系数
    correlation = df['房龄（年）'].corr(df['单价（元/平方米）'])
    plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}',
             transform=plt.gca().transAxes, fontsize=12,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    plt.tight_layout()
    plt.savefig('house_age_price_scatter.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 条形图：各区域平均房价排名
    plt.figure(figsize=(14, 8))

    # 计算各区域平均房价
    area_avg_price = df.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=False)
    area_counts = df['所在区'].value_counts()

    # 只显示数据量大于50的区域
    valid_areas = area_counts[area_counts >= 50].index
    area_avg_price = area_avg_price[area_avg_price.index.isin(valid_areas)]

    bars = plt.bar(range(len(area_avg_price)), area_avg_price.values,
                   color=plt.cm.Blues(np.linspace(0.4, 0.9, len(area_avg_price))))

    plt.title('北京各区域二手房平均单价排名', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('区域', fontsize=12, fontweight='bold')
    plt.ylabel('平均单价（元/平方米）', fontsize=12, fontweight='bold')
    plt.xticks(range(len(area_avg_price)), area_avg_price.index, rotation=45)
    plt.grid(True, alpha=0.3, axis='y')

    # 添加数值标签
    for i, (area, price) in enumerate(area_avg_price.items()):
        plt.text(i, price + 1000, f'{price:.0f}',
                ha='center', va='bottom', fontweight='bold', fontsize=10)
        # 添加样本数量
        count = area_counts[area]
        plt.text(i, price/2, f'n={count}',
                ha='center', va='center', fontsize=8, color='white', fontweight='bold')

    plt.tight_layout()
    plt.savefig('house_area_price_ranking.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_restaurant_visualizations(df):
    """创建餐厅消费数据的可视化图表"""

    # 4. 小提琴图：不同顾客类型的消费金额分布
    plt.figure(figsize=(12, 8))

    # 创建小提琴图
    violin_parts = plt.violinplot([df[df['顾客类型'] == customer_type]['消费金额（元）'].values
                                  for customer_type in df['顾客类型'].unique()],
                                 positions=range(len(df['顾客类型'].unique())),
                                 showmeans=True, showmedians=True)

    # 设置小提琴图颜色
    colors = [COLORS['primary'], COLORS['secondary']]
    for i, pc in enumerate(violin_parts['bodies']):
        pc.set_facecolor(colors[i % len(colors)])
        pc.set_alpha(0.7)

    plt.title('不同顾客类型消费金额分布对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('顾客类型', fontsize=12, fontweight='bold')
    plt.ylabel('消费金额（元）', fontsize=12, fontweight='bold')
    plt.xticks(range(len(df['顾客类型'].unique())), df['顾客类型'].unique())
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    for i, customer_type in enumerate(df['顾客类型'].unique()):
        data = df[df['顾客类型'] == customer_type]['消费金额（元）']
        mean_val = data.mean()
        median_val = data.median()
        plt.text(i, mean_val, f'均值: {mean_val:.1f}',
                ha='center', va='bottom', fontsize=9, fontweight='bold')
        plt.text(i, median_val, f'中位数: {median_val:.1f}',
                ha='center', va='top', fontsize=9, fontweight='bold')

    plt.tight_layout()
    plt.savefig('restaurant_customer_violin.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 5. 热力图：分店与顾客满意度关系
    plt.figure(figsize=(12, 8))

    # 创建透视表
    pivot_table = df.pivot_table(values='顾客满意度',
                                index='分店',
                                columns='顾客类型',
                                aggfunc='mean')

    # 创建热力图
    sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlBu_r',
                center=pivot_table.values.mean(),
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})

    plt.title('各分店不同顾客类型满意度热力图', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('顾客类型', fontsize=12, fontweight='bold')
    plt.ylabel('分店', fontsize=12, fontweight='bold')
    plt.xticks(rotation=0)
    plt.yticks(rotation=0)

    plt.tight_layout()
    plt.savefig('restaurant_satisfaction_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 6. 饼图：顾客类型占比分析
    plt.figure(figsize=(10, 8))

    # 计算顾客类型占比
    customer_counts = df['顾客类型'].value_counts()

    # 创建饼图
    colors = [COLORS['primary'], COLORS['secondary']]
    wedges, texts, autotexts = plt.pie(customer_counts.values,
                                      labels=customer_counts.index,
                                      autopct='%1.1f%%',
                                      colors=colors,
                                      explode=(0.05, 0.05),
                                      shadow=True,
                                      startangle=90)

    # 设置文本样式
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')

    plt.title('餐厅顾客类型分布', fontsize=16, fontweight='bold', pad=20)

    # 添加图例
    plt.legend(wedges, [f'{label}: {count}人' for label, count in customer_counts.items()],
              title="顾客类型统计",
              loc="center left",
              bbox_to_anchor=(1, 0, 0.5, 1))

    plt.tight_layout()
    plt.savefig('restaurant_customer_pie.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_marketing_visualizations(df):
    """创建营销数据的可视化图表"""

    # 7. 折线图：营销费用与订单金额时间趋势
    # 创建双y轴图
    fig, ax1 = plt.subplots(figsize=(14, 8))

    # 左y轴：营销费用
    color1 = COLORS['primary']
    ax1.set_xlabel('日期', fontsize=12, fontweight='bold')
    ax1.set_ylabel('营销费用（元）', color=color1, fontsize=12, fontweight='bold')
    line1 = ax1.plot(df['日期'], df['营销费用（元）'], color=color1, linewidth=3,
                     marker='o', markersize=6, label='营销费用')
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.grid(True, alpha=0.3)

    # 右y轴：订单金额
    ax2 = ax1.twinx()
    color2 = COLORS['secondary']
    ax2.set_ylabel('订单金额（元）', color=color2, fontsize=12, fontweight='bold')
    line2 = ax2.plot(df['日期'], df['订单金额（元）'], color=color2, linewidth=3,
                     marker='s', markersize=6, label='订单金额')
    ax2.tick_params(axis='y', labelcolor=color2)

    # 设置x轴标签
    ax1.tick_params(axis='x', rotation=45)

    plt.title('营销费用与订单金额时间趋势对比', fontsize=16, fontweight='bold', pad=20)

    # 添加图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')

    plt.tight_layout()
    plt.savefig('marketing_trend_line.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 8. 气泡图：展现量、点击量与订单金额三维关系
    plt.figure(figsize=(12, 8))

    # 创建气泡图
    scatter = plt.scatter(df['展现量'], df['点击量'],
                         s=df['订单金额（元）']*0.5,  # 气泡大小表示订单金额
                         c=df['营销费用（元）'],       # 颜色表示营销费用
                         cmap='viridis', alpha=0.7,
                         edgecolors='white', linewidth=1)

    plt.colorbar(scatter, label='营销费用（元）')
    plt.title('营销效果三维关系分析\n（气泡大小表示订单金额，颜色表示营销费用）',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('展现量', fontsize=12, fontweight='bold')
    plt.ylabel('点击量', fontsize=12, fontweight='bold')
    plt.grid(True, alpha=0.3)

    # 添加相关系数信息
    corr_show_click = df['展现量'].corr(df['点击量'])
    corr_click_order = df['点击量'].corr(df['订单金额（元）'])
    plt.text(0.05, 0.95, f'展现量-点击量相关系数: {corr_show_click:.3f}',
             transform=plt.gca().transAxes, fontsize=10,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    plt.text(0.05, 0.88, f'点击量-订单金额相关系数: {corr_click_order:.3f}',
             transform=plt.gca().transAxes, fontsize=10,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    plt.tight_layout()
    plt.savefig('marketing_bubble_chart.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 9. 面积图：各项营销指标的时间变化趋势
    plt.figure(figsize=(14, 8))

    # 选择要显示的指标并进行标准化
    metrics = ['展现量', '点击量', '加购数', '进店数', '商品关注数']
    df_normalized = df.copy()

    # 对数据进行标准化处理（0-1缩放）
    for metric in metrics:
        df_normalized[f'{metric}_norm'] = (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min())

    # 创建面积图
    plt.stackplot(df['日期'],
                  df_normalized['展现量_norm'],
                  df_normalized['点击量_norm'],
                  df_normalized['加购数_norm'],
                  df_normalized['进店数_norm'],
                  df_normalized['商品关注数_norm'],
                  labels=metrics,
                  colors=COLORS['palette'][:5],
                  alpha=0.8)

    plt.title('营销指标时间变化趋势（标准化）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('日期', fontsize=12, fontweight='bold')
    plt.ylabel('标准化数值（0-1）', fontsize=12, fontweight='bold')
    plt.legend(loc='upper left', bbox_to_anchor=(1, 1))
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('marketing_area_chart.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 加载数据
    datasets = load_datasets()

    print("开始生成二手房数据可视化图表...")
    create_house_visualizations(datasets['house'])
    print("二手房数据可视化图表生成完成！")

    print("\n开始生成餐厅数据可视化图表...")
    create_restaurant_visualizations(datasets['restaurant'])
    print("餐厅数据可视化图表生成完成！")

    print("\n开始生成营销数据可视化图表...")
    create_marketing_visualizations(datasets['marketing'])
    print("营销数据可视化图表生成完成！")
